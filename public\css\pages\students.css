/* Students Page - Clean Styles */

/* Student Categories */
.student-categories {
    background-color: white;
    border: 1px solid #ddd;
    padding: 20px;
    margin-bottom: 30px;
}

.category-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.tab-btn {
    padding: 12px 20px;
    border: 2px solid #052F11;
    background-color: white;
    color: #052F11;
    cursor: pointer;
    font-weight: 500;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tab-btn:hover {
    background-color: #052F11;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(5, 47, 17, 0.3);
}

.tab-btn.active {
    background-color: #052F11;
    color: white;
    box-shadow: 0 0 15px rgba(5, 47, 17, 0.4);
    border: 2px solid rgba(5, 47, 17, 0.8);
    font-weight: 600;
}

/* Add a subtle glow effect when tab is being viewed */
.tab-btn.viewing {
    box-shadow: 0 0 10px rgba(5, 47, 17, 0.3);
    border-color: rgba(5, 47, 17, 0.7);
}

/* Enhanced hover animations */
.tab-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.tab-btn:hover::before {
    left: 100%;
}

/* Pulse animation for active tab */
@keyframes pulse {
    0% {
        box-shadow: 0 0 15px rgba(5, 47, 17, 0.4);
    }
    50% {
        box-shadow: 0 0 20px rgba(5, 47, 17, 0.6);
    }
    100% {
        box-shadow: 0 0 15px rgba(5, 47, 17, 0.4);
    }
}

.tab-btn.active {
    animation: pulse 2s infinite;
}

/* Smooth transitions for all states */
.tab-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-btn:active {
    transform: scale(0.95);
}

/* Student Table Container */
.student-table-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-header {
    background-color: #f8f9fa;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
}

.table-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.table-actions {
    display: flex;
    gap: 10px;
}

.btn-primary {
    background: #052F11;
    color: white;
    border: 1px solid #052F11;
    padding: 10px 16px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: rgba(5, 47, 17, 0.8);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(5, 47, 17, 0.3);
}

.btn-secondary {
    background: rgba(5, 47, 17, 0.6);
    color: white;
    border: 1px solid rgba(5, 47, 17, 0.6);
    padding: 10px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #052F11;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(5, 47, 17, 0.3);
}

.export-btn {
    background-color: rgba(5, 47, 17, 0.7);
    color: white;
    border: 1px solid rgba(5, 47, 17, 0.7);
    padding: 10px 15px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.export-btn:hover {
    background-color: #052F11;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(5, 47, 17, 0.3);
}

/* Students Table */
.students-table {
    width: 100%;
    border-collapse: collapse;
}

.students-table th {
    background-color: #f8f9fa;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #eee;
    font-size: 14px;
}

.students-table td {
    padding: 15px;
    border-bottom: 1px solid #eee;
    font-size: 14px;
    color: #333;
}

.students-table tr:hover {
    background-color: #f8f9fa;
}

.students-table tr:last-child td {
    border-bottom: none;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.empty-state i {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 15px;
}

.empty-state h3 {
    margin: 0 0 10px;
    color: #999;
}

.empty-state p {
    margin: 0;
    font-size: 14px;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-badge.approved {
    background-color: rgba(5, 47, 17, 0.1);
    color: #052F11;
    border: 1px solid rgba(5, 47, 17, 0.3);
}

.status-badge.rejected {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-badge.active {
    background-color: rgba(5, 47, 17, 0.1);
    color: #052F11;
    border: 1px solid rgba(5, 47, 17, 0.3);
}

.status-badge.inactive {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Action Buttons */
.action-btn {
    padding: 5px 8px;
    margin: 0 2px;
    border: 1px solid #052F11;
    cursor: pointer;
    font-size: 12px;
    background-color: #052F11;
    color: white;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
    border-radius: 3px;
}

.action-btn:hover {
    background-color: rgba(5, 47, 17, 0.8);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(5, 47, 17, 0.3);
}

.action-btn.view {
    background-color: #052F11;
    border-color: #052F11;
}

.action-btn.edit {
    background-color: rgba(5, 47, 17, 0.7);
    border-color: rgba(5, 47, 17, 0.7);
}

.action-btn.delete {
    background-color: #dc3545;
    border-color: #dc3545;
}

.action-btn.delete:hover {
    background-color: #c82333;
    border-color: #c82333;
}

/* Scholarship Type Badges */
.scholarship-type {
    padding: 4px 8px;
    font-size: 12px;
    background: rgba(5, 47, 17, 0.1);
    color: #052F11;
    border: 1px solid rgba(5, 47, 17, 0.3);
    border-radius: 4px;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
    background-color: #052F11;
}

/* Semester and Academic Year Badges */
.semester-badge {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
    border: 1px solid #ffeaa7;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
}

.academic-year-badge {
    background: linear-gradient(135deg, #e8f5e8, #d4edda);
    color: #155724;
    border: 1px solid #c3e6cb;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
}

/* Benefactor Type Badge */
.benefactor-badge {
    background: linear-gradient(135deg, #e3f2fd, #e1f5fe);
    color: #0277bd;
    border: 1px solid #29b6f6;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
}

/* Classification Badge for Academic */
.classification-badge {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
    border: 1px solid #ffeaa7;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .category-tabs {
        flex-direction: column;
        gap: 8px;
    }

    .tab-btn {
        padding: 10px 15px;
        font-size: 14px;
        text-align: center;
    }

    .table-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .table-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .students-table {
        font-size: 12px;
    }

    .students-table th,
    .students-table td {
        padding: 10px 8px;
    }

    /* Hide less important columns on mobile */
    .students-table th:nth-child(3),
    .students-table td:nth-child(3),
    .students-table th:nth-child(5),
    .students-table td:nth-child(5) {
        display: none;
    }
}

@media (max-width: 480px) {
    .student-categories {
        padding: 15px;
    }

    .table-header {
        padding: 15px;
    }

    .table-header h3 {
        font-size: 16px;
    }

    .export-btn {
        padding: 8px 12px;
        font-size: 12px;
    }
}

/* Modal Styles */
.modal {
    display: none !important;
    position: fixed !important;
    z-index: 1000 !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    backdrop-filter: blur(2px);
}

.modal[style*="display: block"],
.modal.modal-show {
    display: block !important;
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    background: linear-gradient(135deg, #1e5631, #2e7d32);
    color: white;
    padding: 20px 25px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s ease;
    line-height: 1;
}

.close:hover {
    color: #ffcdd2;
}

.modal-body {
    padding: 25px;
}

.form-section {
    margin-bottom: 25px;
}

.form-section h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
    padding-bottom: 8px;
    border-bottom: 2px solid #e9ecef;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #fafafa;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #1e5631;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(30, 86, 49, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* Subjects Section Styles */
.subject-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 10px;
    background-color: #fafafa;
    transition: all 0.3s ease;
}

.subject-row:hover {
    background-color: #f0f0f0;
    border-color: #1e5631;
}

.subject-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.subject-code {
    font-weight: 600;
    color: #1e5631;
    font-size: 14px;
}

.subject-title {
    color: #333;
    font-size: 13px;
}

.subject-units {
    color: #666;
    font-size: 12px;
    font-style: italic;
}

.grade-input {
    width: 100px;
}

.grade-input input {
    width: 100%;
    padding: 8px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    text-align: center;
    transition: all 0.3s ease;
}

.grade-input input:focus {
    outline: none;
    border-color: #1e5631;
    box-shadow: 0 0 0 3px rgba(30, 86, 49, 0.1);
}

.no-subjects-message {
    text-align: center;
    padding: 30px;
    color: #666;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px dashed #ccc;
}

.no-subjects-message p {
    margin: 5px 0;
}

.no-subjects-message p:first-child {
    font-weight: 600;
    color: #333;
}

.form-group input[readonly] {
    background-color: #f5f5f5;
    color: #666;
    cursor: not-allowed;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background-color: #fafafa;
    border-radius: 0 0 12px 12px;
}

.modal-footer .btn-secondary,
.modal-footer .btn-primary {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-size: 14px;
}

.modal-footer .btn-secondary {
    background-color: #6c757d;
    color: white;
}

.modal-footer .btn-secondary:hover {
    background-color: #5a6268;
    transform: translateY(-1px);
}

.modal-footer .btn-primary {
    background: linear-gradient(135deg, #1e5631, #2e7d32);
    color: white;
}

.modal-footer .btn-primary:hover {
    background: linear-gradient(135deg, #164023, #1b5e20);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(30, 86, 49, 0.3);
}

.modal-footer .btn-primary:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1001;
    display: flex;
    align-items: center;
    gap: 10px;
    animation: slideInRight 0.3s ease-out;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.notification.success {
    background: linear-gradient(135deg, #4caf50, #2e7d32);
}

.notification.error {
    background: linear-gradient(135deg, #f44336, #c62828);
}

.notification i {
    font-size: 18px;
}

/* Modal Responsive Design */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .modal-header {
        padding: 15px 20px;
    }

    .modal-body {
        padding: 20px;
    }

    .modal-footer {
        padding: 15px 20px;
        flex-direction: column;
    }

    .modal-footer .btn-secondary,
    .modal-footer .btn-primary {
        width: 100%;
        justify-content: center;
    }
}
